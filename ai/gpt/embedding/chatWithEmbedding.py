import json

import requests

from setup_vector import get_API_token_AI_CORE
from setup_vector import query_from_doc

auth_token = get_API_token_AI_CORE()
svc_url = "https://azure-openai-serv-i057149.cfapps.sap.hana.ondemand.com"
chat_cache = []
user_input_cache = []
current_context = ""


def get_new_context(question, msg):
    print(f"--------------- getting new context of msg: {msg}, summary question: {question}")
    global current_context
    current_context = query_from_doc(question)
    return get_completion(msg, with_new_context=True)


def get_page_info():
    return "Please provide page information"


available_functions = {
    "get_new_context": get_new_context,
    "get_page_info": get_page_info,
}


def get_completion(msg, with_new_context=False):
    global user_input_cache
    global chat_cache
    global current_context
    user_input_cache.append(msg)
    if (len(user_input_cache)) > 8:
        user_input_cache.pop(0)
    if not with_new_context and current_context == '':
        print("------------- get context for the first question")
        current_context = query_from_doc(msg)
    prompt = f"""Answer the Question only based on Chat History and Context
    Context: {current_context}
    
    Chat History: {chat_cache}
    
    Question: {msg}
    isNewContext: {with_new_context}
    """
    chat_cache.append({"role": "user", "content": msg})
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "AI-Resource-Group": "default",
    }
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_new_context",
                "description":
                    """
                    get new or more area specific context from embedded documents when current context and chat history cannot
                    support or not enough to answer the latest question
                    """,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "question": {
                            "type": "string",
                            "description": "a standalone question based on the latest question and the chat history"
                        },
                        "msg": {
                            "type": "string",
                            "description": "the original latest user input"
                        }
                    },
                    "required": ["question", "msg"]
                },
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_page_info",
                "description": "provide page info when user ask what can I do or what can he/she do",
                "parameters": {"type": "object", "properties": {}}
            }
        }
    ]
    # won't use function call when the get_completion is triggered by function call
    tool_choice = "none" if with_new_context else "auto"

    # If the user is asking a question, and you cannot answer the user's question based on current "Context" and
    # "Chat History", call the function get_new_context().

    # V1 prompt
    # I want you to act as a document that I am having a conversation with. Your name is "Readiness Check AI Assistant".
    # Using the provided context, answer the user's question to the best of your ability using the resources provided.
    # if you find the "Context" is not enough for user's question or the "Context" cannot support to answer user's question,
    # or the user is asking for some more detailed information, just call the function get_new_context.
    # if there is no specific information about the user's question call the functino get_new_context.
    # When the user provide detailed information related to the last question, call functino get_new_context.
    # Never make up a answer.
    # Refuse to answer any question not about the info. Never break character.
    request_body = {
        # "deployment_id": "gpt-35-turbo-16k",
        "tools": tools,
        "tool_choice": tool_choice,
        "messages": [
            {"role": "system",
             "content":
                 """
Act as the "Readiness Check AI Assistant, answer the Question only based on Chat History and Context. 
If isNewContext is false and the information within the current context and chat history is insufficient to provide 
a complete answer, use the function get_new_context to retrieve additional data.
If you cannot answer the question when isNewContext is true, just say I don't know.
"""
             },
            {"role": "user", "content": prompt}
        ]
    }

    ai_core_url = "https://api.ai.internalprod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/d3defda2415de8bd/chat/completions?api-version=2024-02-01"
    # response = requests.post(f"{svc_url}/api/v1/completions", headers=headers, json=request_body).json()
    response = requests.post(ai_core_url, headers=headers, json=request_body).json()
    print("full response:", json.dumps(response, indent=2))
    if response['choices'][0]['finish_reason'] == "tool_calls":
        reply = response['choices'][0]['message']
        func_to_call = available_functions[reply['tool_calls'][0]['function']['name']]
        print("------------- function call triggered: ", func_to_call.__name__)
        return func_to_call(**json.loads(reply['tool_calls'][0]['function']['arguments']))

    # print("full response: ", response)
    chat_cache.append(response['choices'][0]['message'])
    print(f"chat Cache: {chat_cache}")
    print(f"user inputs: {user_input_cache}")
    return response['choices'][0]['message']['content']

def get_completion_from_API(msg):
    global chat_cache
    headers = {
        "Authorization": "Basic ************************************",
        "Content-Type": "application/json",
    }
    request_body = {
        "query": msg,
        "chat_history": chat_cache
    }
    local_url = "http://localhost:5000"
    cloud_url = "https://rcknowledgeapi.cfapps.sap.hana.ondemand.com"
    api_35 = "/gpt35_with_history"
    api_4 = "/gpt4_with_history"
    old_api = ""
    response = requests.post(f"{local_url}/api{api_35}", headers=headers, json=request_body).json()
    chat_cache.append({"role": "user", "content": msg})
    chat_cache.append({"role": "assistant", "content": response['result']})
    return response['result']

